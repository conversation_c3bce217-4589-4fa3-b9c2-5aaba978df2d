const updateProfileSchema = {
    body: {
        type: "object",
        properties: {
            first_name: {
                type: "string",
                minLength: 1,
                maxLength: 225, 
                errorMessage: {
                    type: "First name must be a string",
                    minLength: "First name must be at least 1 character long",
                    maxLength: "First name must not exceed 225 characters",
                },
            },
            last_name: {
                type: "string",
                minLength: 1,
                maxLength: 225, 
                errorMessage: {
                    type: "Last name must be a string",
                    minLength: "Last name must be at least 1 character long",
                    maxLength: "Last name must not exceed 225 characters",
                },
            },
            email: {
                type: "string",
                format: "email",
                minLength: 5, 
                maxLength: 80, 
                errorMessage: {
                    type: "Email must be a string",
                    format: "Please enter a valid email address",
                    minLength: "Email must be at least 5 characters long",
                    maxLength: "Email must not exceed 80 characters",
                },
            },
            mobile: {
                type: "string",
                pattern: "^[0-9]+$",
                minLength: 7,
                maxLength: 13,
                errorMessage: {
                    type: "Mobile number must be a string",
                    pattern: "Mobile number can only contain digits",
                    minLength: "Mobile number must be at least 7 digits long",
                    maxLength: "Mobile number must not exceed 13 digits",
                },
            },
            gender: {
                type: "string",
                enum: ["male", "female", "other", "Male", "Female", "Other"],
                errorMessage: {
                    type: "Gender must be a string",
                    enum: "Gender must be one of: male, female, other",
                },
            },
            nationality: {
                type: "string",
                minLength: 1,
                maxLength: 225, 
                pattern: "^[0-9]+$",
                errorMessage: {
                    type: "Nationality must be a string",
                    minLength: "Nationality must be at least 2 characters long",
                    maxLength: "Nationality must not exceed 225 characters",
                    pattern:
                        "Nationality can only contain numbers",
                },
            },
            offersale: {
                type: "boolean",
                errorMessage: {
                    type: "offersale must be a boolean",
                },
            },
            agreeTerms: {
                type: "boolean",
                errorMessage: {
                    type: "agreeTerms must be a boolean",
                },
            },
        },
        additionalProperties: false,
        errorMessage: {
            additionalProperties:
                "Only first_name, last_name, email, mobile, gender, and nationality are allowed",
        },
    },
};

export {
    updateProfileSchema,
};
